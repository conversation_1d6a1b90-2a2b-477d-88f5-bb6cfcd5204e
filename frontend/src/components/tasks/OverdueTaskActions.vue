<template>
  <div v-if="isTaskOverdue(task)" class="flex items-center space-x-2 mt-2">
    <button
      @click="handleExtendDeadline"
      class="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 border border-orange-200 rounded hover:bg-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-800 dark:hover:bg-orange-900/30 transition-colors"
      title="延期任务"
    >
      📅 延期
    </button>
    
    <button
      @click="handleMarkComplete"
      class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 border border-green-200 rounded hover:bg-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800 dark:hover:bg-green-900/30 transition-colors"
      title="标记完成"
    >
      ✅ 完成
    </button>
    
    <button
      @click="handleDelete"
      class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 border border-red-200 rounded hover:bg-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800 dark:hover:bg-red-900/30 transition-colors"
      title="删除任务"
    >
      🗑️ 删除
    </button>
  </div>
</template>

<script setup lang="ts">
import { useTasksStore } from '@/stores/tasks'
import type { Task } from '@/types'
import { isTaskOverdue } from '@/utils/task'

interface Props {
  task: Task
}

interface Emits {
  taskUpdated: [task: Task]
  taskDeleted: [taskId: number]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const tasksStore = useTasksStore()

// 方法
const handleExtendDeadline = async () => {
  try {
    // 延期7天
    const currentDate = props.task.isLongTerm && props.task.endDate 
      ? new Date(props.task.endDate) 
      : props.task.dueDate 
      ? new Date(props.task.dueDate)
      : new Date()
    
    const newDate = new Date(currentDate)
    newDate.setDate(newDate.getDate() + 7)
    
    const updateData = props.task.isLongTerm 
      ? { endDate: newDate.toISOString().split('T')[0] }
      : { dueDate: newDate.toISOString().split('T')[0] }
    
    const updatedTask = await tasksStore.updateTask(props.task.id, updateData)
    emit('taskUpdated', updatedTask)
  } catch (error) {
    console.error('延期任务失败:', error)
  }
}

const handleMarkComplete = async () => {
  try {
    const updatedTask = await tasksStore.updateTask(props.task.id, { status: 'completed' })
    emit('taskUpdated', updatedTask)
  } catch (error) {
    console.error('标记完成失败:', error)
  }
}

const handleDelete = async () => {
  const confirmed = confirm(`确定要删除逾期任务"${props.task.title}"吗？\n\n此操作无法撤销。`)
  
  if (confirmed) {
    try {
      await tasksStore.deleteTask(props.task.id)
      emit('taskDeleted', props.task.id)
    } catch (error) {
      console.error('删除任务失败:', error)
    }
  }
}
</script>
