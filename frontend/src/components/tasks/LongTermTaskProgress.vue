<template>
  <div v-if="task.isLongTerm && task.startDate && task.endDate" class="mt-2">
    <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
      <span>进度</span>
      <span>{{ progressPercentage }}%</span>
    </div>
    
    <!-- 进度条 -->
    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
      <div 
        :class="[
          'h-2 rounded-full transition-all duration-300',
          task.status === 'completed' 
            ? 'bg-green-500' 
            : isOverdue 
            ? 'bg-red-500' 
            : progressPercentage >= 80 
            ? 'bg-orange-500' 
            : 'bg-blue-500'
        ]"
        :style="{ width: `${Math.min(progressPercentage, 100)}%` }"
      ></div>
    </div>
    
    <!-- 时间信息 -->
    <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
      <span>{{ formatDate(task.startDate) }}</span>
      <span>{{ remainingDaysText }}</span>
      <span>{{ formatDate(task.endDate) }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Task } from '@/types'
import { isTaskOverdue, getTaskRemainingDays } from '@/utils/task'

interface Props {
  task: Task
}

const props = defineProps<Props>()

// 计算属性
const isOverdue = computed(() => isTaskOverdue(props.task))

const progressPercentage = computed(() => {
  if (!props.task.startDate || !props.task.endDate) return 0
  
  const startDate = new Date(props.task.startDate)
  const endDate = new Date(props.task.endDate)
  const currentDate = new Date()
  
  startDate.setHours(0, 0, 0, 0)
  endDate.setHours(0, 0, 0, 0)
  currentDate.setHours(0, 0, 0, 0)
  
  const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  const passedDays = Math.ceil((currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  
  if (totalDays <= 0) return 0
  
  return Math.round((passedDays / totalDays) * 100)
})

const remainingDaysText = computed(() => {
  const remainingDays = getTaskRemainingDays(props.task)
  
  if (remainingDays === null) return ''
  
  if (remainingDays < 0) {
    return `逾期 ${Math.abs(remainingDays)} 天`
  } else if (remainingDays === 0) {
    return '今天到期'
  } else if (remainingDays === 1) {
    return '明天到期'
  } else {
    return `还有 ${remainingDays} 天`
  }
})

// 方法
const formatDate = (dateString: string): string => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}
</script>
