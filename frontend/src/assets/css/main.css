@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', 'Noto Sans SC', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    @apply transition-colors duration-200;
  }
}

@layer components {
  /* 按钮组件样式 */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
    @apply focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300;
    @apply dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600;
    @apply focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply bg-transparent text-gray-600 hover:bg-gray-100;
    @apply dark:text-gray-400 dark:hover:bg-gray-800;
    @apply focus:ring-gray-500;
  }
  
  /* 输入框样式 */
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
    @apply dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100;
    @apply transition-all duration-200;
  }
  
  /* 卡片样式 */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
    @apply transition-all duration-200;
  }
  
  /* 侧边栏样式 */
  .sidebar {
    @apply bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700;
    @apply transition-all duration-200;
  }
}

@layer utilities {
  /* 自定义滚动条 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }
  
  /* 动画工具类 */
  .animate-fade-in {
    animation: fadeIn 0.2s ease-in-out;
  }
  
  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }
}
