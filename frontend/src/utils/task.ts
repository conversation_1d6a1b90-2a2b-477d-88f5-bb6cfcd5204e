import type { Task } from '@/types'

/**
 * 检查任务是否逾期
 * @param task 任务对象
 * @returns 是否逾期
 */
export function isTaskOverdue(task: Task): boolean {
  // 已完成的任务不算逾期
  if (task.status === 'completed') {
    return false
  }

  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // 长期任务检查结束日期
  if (task.isLongTerm && task.endDate) {
    const endDate = new Date(task.endDate)
    endDate.setHours(0, 0, 0, 0)
    return endDate < today
  }

  // 普通任务检查截止日期
  if (task.dueDate) {
    const dueDate = new Date(task.dueDate)
    dueDate.setHours(0, 0, 0, 0)
    return dueDate < today
  }

  // 没有截止日期的任务不算逾期
  return false
}

/**
 * 为任务添加逾期状态
 * @param task 任务对象
 * @returns 带有逾期状态的任务对象
 */
export function addOverdueStatus(task: Task): Task {
  return {
    ...task,
    isOverdue: isTaskOverdue(task)
  }
}

/**
 * 为任务列表添加逾期状态
 * @param tasks 任务列表
 * @returns 带有逾期状态的任务列表
 */
export function addOverdueStatusToTasks(tasks: Task[]): Task[] {
  return tasks.map(addOverdueStatus)
}

/**
 * 获取逾期任务列表
 * @param tasks 任务列表
 * @returns 逾期任务列表
 */
export function getOverdueTasks(tasks: Task[]): Task[] {
  return tasks.filter(isTaskOverdue)
}

/**
 * 计算任务的剩余天数
 * @param task 任务对象
 * @returns 剩余天数，负数表示逾期天数
 */
export function getTaskRemainingDays(task: Task): number | null {
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  let targetDate: Date | null = null

  // 长期任务使用结束日期
  if (task.isLongTerm && task.endDate) {
    targetDate = new Date(task.endDate)
  }
  // 普通任务使用截止日期
  else if (task.dueDate) {
    targetDate = new Date(task.dueDate)
  }

  if (!targetDate) {
    return null
  }

  targetDate.setHours(0, 0, 0, 0)
  const diffTime = targetDate.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return diffDays
}

/**
 * 获取任务的紧急程度
 * @param task 任务对象
 * @returns 紧急程度：'overdue' | 'urgent' | 'normal' | 'none'
 */
export function getTaskUrgency(task: Task): 'overdue' | 'urgent' | 'normal' | 'none' {
  if (task.status === 'completed') {
    return 'none'
  }

  const remainingDays = getTaskRemainingDays(task)

  if (remainingDays === null) {
    return 'none'
  }

  if (remainingDays < 0) {
    return 'overdue'
  }

  if (remainingDays <= 1) {
    return 'urgent'
  }

  if (remainingDays <= 3) {
    return 'normal'
  }

  return 'none'
}
