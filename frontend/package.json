{"name": "zentask-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "@heroicons/vue": "^2.0.18", "@vueuse/core": "^10.5.0", "dayjs": "^1.11.10", "vue-draggable-plus": "^0.3.5"}, "devDependencies": {"@types/node": "^20.8.10", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "autoprefixer": "^10.4.16", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "tailwindcss": "^3.3.5", "typescript": "~5.2.0", "vite": "^4.4.11", "vue-tsc": "^1.8.19"}}