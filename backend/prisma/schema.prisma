// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id           Int       @id @default(autoincrement())
  email        String    @unique @db.VarChar(255)
  passwordHash String    @map("password_hash") @db.VarChar(255)
  createdAt    DateTime  @default(now()) @map("created_at")
  
  // 关联关系
  projects     Project[]
  tasks        Task[]
  
  @@map("users")
}

model Project {
  id          Int      @id @default(autoincrement())
  userId      Int      @map("user_id")
  name        String   @db.VarChar(100)
  showInToday Boolean  @default(true) @map("show_in_today")
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联关系
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  tasks       Task[]

  @@map("projects")
}

model Task {
  id          Int        @id @default(autoincrement())
  userId      Int        @map("user_id")
  projectId   Int        @map("project_id")
  title       String     @db.VarChar(255)
  description String?    @db.Text
  status      TaskStatus @default(PENDING)
  priority    Priority   @default(MEDIUM)
  dueDate     DateTime?  @map("due_date") @db.Date
  isLongTerm  Boolean    @default(false) @map("is_long_term")
  startDate   DateTime?  @map("start_date") @db.Date
  endDate     DateTime?  @map("end_date") @db.Date
  sortOrder   Int        @default(0) @map("sort_order")
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")

  // 关联关系
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  project     Project    @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@map("tasks")
}

enum TaskStatus {
  PENDING   @map("pending")
  COMPLETED @map("completed")
}

enum Priority {
  LOW    @map("low")
  MEDIUM @map("medium")
  HIGH   @map("high")
}
